/**
 * Sidebar Menu Enhancement
 * 
 * Enhances the mobile sidebar menu functionality to allow both the "+" button 
 * and the menu link text to expand/collapse submenus.
 * 
 * Only targets the sidebar menu with class "side-info" to avoid affecting other menus.
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

$(document).ready(function() {
    
    /**
     * Initialize sidebar menu enhancements after a short delay
     * to ensure meanmenu.js has finished initializing
     */
    setTimeout(function() {
        initializeSidebarMenuEnhancement();
    }, 500);
    
    /**
     * Main function to enhance sidebar menu functionality
     */
    function initializeSidebarMenuEnhancement() {
        
        // Only target the sidebar menu container
        const sidebarContainer = $('.side-info .mean-container');
        
        // Check if meanmenu has been initialized in the sidebar
        if (sidebarContainer.length === 0) {
            console.log('Sidebar meanmenu not found, skipping enhancement');
            return;
        }
        
        console.log('Initializing sidebar menu enhancement');
        
        // Find all menu links within the sidebar that have corresponding expand buttons
        const menuLinksWithSubmenus = sidebarContainer.find('.mean-nav ul li a').filter(function() {
            // Check if this link's parent li has a .mean-expand button
            return $(this).siblings('.mean-expand').length > 0;
        });
        
        console.log('Found ' + menuLinksWithSubmenus.length + ' menu links with submenus');
        
        // Add click event listeners to menu links that have submenus
        menuLinksWithSubmenus.each(function() {
            const $menuLink = $(this);
            const $expandButton = $menuLink.siblings('.mean-expand');

            // Remove any existing click handlers to prevent duplicates
            $menuLink.off('click.sidebarEnhancement');

            // Add click event listener to the menu link with namespace
            $menuLink.on('click.sidebarEnhancement', function(e) {
                
                // Get the href attribute
                const href = $menuLink.attr('href');
                
                // If the link is "#" or empty, it's meant for submenu expansion
                if (!href || href === '#' || href === 'javascript:void(0)') {
                    e.preventDefault();
                    
                    // Trigger click on the corresponding expand button
                    if ($expandButton.length > 0) {
                        console.log('Triggering expand button for menu link');
                        $expandButton.trigger('click');
                    }
                } else {
                    // For links with actual URLs, let them navigate normally
                    // but also trigger expansion if they have submenus
                    if ($expandButton.length > 0) {
                        e.preventDefault();
                        console.log('Triggering expand button for menu link with URL');
                        $expandButton.trigger('click');
                    }
                }
            });
            
            // Add visual feedback to indicate the link is clickable
            $menuLink.css({
                'cursor': 'pointer'
            });
        });
        
        // Remove existing hover handlers and add new ones to prevent duplicates
        menuLinksWithSubmenus.off('mouseenter.sidebarEnhancement mouseleave.sidebarEnhancement');

        // Add hover effects to menu links with submenus for better UX
        menuLinksWithSubmenus.hover(
            function() {
                $(this).css('opacity', '0.8');
            },
            function() {
                $(this).css('opacity', '1');
            }
        );
        
        console.log('Sidebar menu enhancement initialized successfully');
    }
    
    /**
     * Re-initialize enhancement if the sidebar is toggled
     * (in case meanmenu reinitializes)
     */
    $(document).on('click', '.side-toggle, #hamburger', function() {
        setTimeout(function() {
            initializeSidebarMenuEnhancement();
        }, 600);
    });
    
    /**
     * Handle window resize to ensure functionality persists
     */
    $(window).on('resize', function() {
        // Debounce the resize event
        clearTimeout(window.sidebarResizeTimeout);
        window.sidebarResizeTimeout = setTimeout(function() {
            initializeSidebarMenuEnhancement();
        }, 300);
    });
    
});
